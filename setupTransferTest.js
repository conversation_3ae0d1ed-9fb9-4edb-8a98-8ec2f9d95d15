// setupTransferTest.js
import hre from "hardhat";
const { ethers } = hre;
import { ERC20_ABI } from "./lib/erc20Abi.js"; // Adjust path if needed

async function main() {
  const provider = ethers.provider;
  const signer = await ethers.getSigner(); // Uses first account (0xf39F...)

  const tokenInAddress = "******************************************";
  const arbitrageAddress = "******************************************";

  const tokenIn = new ethers.Contract(tokenInAddress, ERC20_ABI, signer);

  const amount = ethers.parseUnits("1.0", 18);

  // Check balance
  const balance = await tokenIn.balanceOf(signer.address);
  console.log("🧮 Signer balance:", ethers.formatUnits(balance, 18));

  // Approve arbitrage contract
  const approveTx = await tokenIn.approve(arbitrageAddress, amount);
  await approveTx.wait();
  console.log("✅ Approval confirmed");

  // Check allowance
  const allowance = await tokenIn.allowance(signer.address, arbitrageAddress);
  console.log("🔍 Allowance:", ethers.formatUnits(allowance, 18));

  // Attempt transferFrom
  const transferTx = await tokenIn.transferFrom(
    signer.address,
    arbitrageAddress,
    amount
  );
  await transferTx.wait();
  console.log("✅ transferFrom succeeded");

  // Check arbitrage contract balance
  const arbBalance = await tokenIn.balanceOf(arbitrageAddress);
  console.log(
    "📦 Arbitrage contract balance:",
    ethers.formatUnits(arbBalance, 18)
  );
}

main().catch((err) => {
  console.error("❌ Setup script failed:", err);
  process.exit(1);
});
