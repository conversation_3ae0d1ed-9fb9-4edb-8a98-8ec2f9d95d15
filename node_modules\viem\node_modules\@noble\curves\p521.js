"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.encodeToCurve = exports.hashToCurve = exports.secp521r1 = exports.p521 = void 0;
const nist_ts_1 = require("./nist.js");
/** @deprecated use `import { p521 } from '@noble/curves/nist.js';` */
exports.p521 = nist_ts_1.p521;
/** @deprecated use `import { p521 } from '@noble/curves/nist.js';` */
exports.secp521r1 = nist_ts_1.p521;
/** @deprecated use `import { p521_hasher } from '@noble/curves/nist.js';` */
exports.hashToCurve = (() => nist_ts_1.p521_hasher.hashToCurve)();
/** @deprecated use `import { p521_hasher } from '@noble/curves/nist.js';` */
exports.encodeToCurve = (() => nist_ts_1.p521_hasher.encodeToCurve)();
//# sourceMappingURL=p521.js.map