"use strict";
/**
 * Audited & minimal JS implementation of elliptic curve cryptography.
 * @module
 * @example
```js
import { secp256k1, schnorr } from '@noble/curves/secp256k1.js';
import { ed25519, ed25519ph, ed25519ctx, x25519, <PERSON><PERSON><PERSON>toPoint } from '@noble/curves/ed25519.js';
import { ed448, ed448ph, ed448ctx, x448 } from '@noble/curves/ed448.js';
import { p256, p384, p521 } from '@noble/curves/nist.js';
import { bls12_381 } from '@noble/curves/bls12-381.js';
import { bn254 } from '@noble/curves/bn254.js';
import { bytesToHex, hexToBytes, concatBytes, utf8ToBytes } from '@noble/curves/abstract/utils.js';
```
 */
throw new Error('root module cannot be imported: import submodules instead. Check out README');
//# sourceMappingURL=index.js.map