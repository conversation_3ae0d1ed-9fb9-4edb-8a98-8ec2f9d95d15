// test_bot.js
import { ethers } from "ethers";
import { getArbitrageContract } from "./lib/getArbitrageContract.js";
import { loadAddresses } from "./lib/loadConfig.js";
import { runStrategy } from "./strategies/simpleSwap.js";
import { ERC20_ABI } from "./lib/erc20Abi.js";

// Connect to local Hardhat node
const provider = new ethers.JsonRpcProvider("http://127.0.0.1:8545");

// Create signer and bind it to provider
const rawSigner = new ethers.Wallet(
  "0xac0974bec39a17e36ba4a6b4d238ff944bacb478cbed5efcae784d7bf4f2ff80"
);
const signer = rawSigner.connect(provider);

// Reset nonce to current network state
async function resetNonce() {
  const currentNonce = await provider.getTransactionCount(
    signer.address,
    "latest"
  );
  console.log("🔄 Current nonce for", signer.address, ":", currentNonce);
  return currentNonce;
}

async function main() {
  await resetNonce();
  const addresses = await loadAddresses();

  const arbitrage = getArbitrageContract(addresses.arbitrage, signer);

  const owner = await arbitrage.getOwner();
  console.log("👑 Arbitrage contract owner:", owner);

  const tokenA = new ethers.Contract(addresses.tokenIn, ERC20_ABI, signer);
  const tokenB = new ethers.Contract(addresses.tokenOut, ERC20_ABI, signer);

  const balanceA = await tokenA.balanceOf(signer.address);
  const balanceB = await tokenB.balanceOf(signer.address);

  console.log("🧮 Signer balances:");
  console.log("   TokenA:", ethers.formatUnits(balanceA, 18));
  console.log("   TokenB:", ethers.formatUnits(balanceB, 18));

  const allowance = await tokenA.allowance(signer.address, addresses.arbitrage);
  console.log(
    "🔍 TokenA allowance for Arbitrage:",
    ethers.formatUnits(allowance, 18)
  );

  try {
    await runStrategy({ arbitrage, addresses, signer });
  } catch (err) {
    console.error("❌ Bot error:", err);
  }
}

main().catch((err) => {
  console.error("❌ Bot error:", err);
});
